<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SpeakMCP</title>
    <link rel="icon" type="image/png" href="favicon.png">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://speakmcp.com/">
    <meta property="og:title" content="SpeakMCP">
    <meta property="og:description" content="Voice-controlled MCP (Model Context Protocol) client for seamless AI interactions">
    <meta property="og:image" content="https://speakmcp.com/og-image.png">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://speakmcp.com/">
    <meta property="twitter:title" content="SpeakMCP">
    <meta property="twitter:description" content="Voice-controlled MCP (Model Context Protocol) client for seamless AI interactions">
    <meta property="twitter:image" content="https://speakmcp.com/og-image.png">
    <style>
        :root {
            /* Modern liquid glass variables */
            --glass-bg: linear-gradient(135deg,
                rgba(255, 255, 255, 0.1) 0%,
                rgba(255, 255, 255, 0.05) 50%,
                rgba(255, 255, 255, 0.1) 100%);
            --glass-border: linear-gradient(135deg,
                rgba(255, 255, 255, 0.3) 0%,
                rgba(255, 255, 255, 0.1) 50%,
                rgba(255, 255, 255, 0.3) 100%);
            --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3),
                           0 2px 8px rgba(0, 0, 0, 0.2),
                           inset 0 1px 0 rgba(255, 255, 255, 0.2);
            --glass-hover-shadow: 0 12px 40px rgba(0, 0, 0, 0.4),
                                 0 4px 12px rgba(0, 0, 0, 0.3),
                                 inset 0 1px 0 rgba(255, 255, 255, 0.3);
            --backdrop-blur: 20px;
            --border-radius: 24px;
        }

        body {
            margin: 0;
            padding: 0;
            background-color: black;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
        }

        .video-background {
            position: fixed;
            top: 0;
            left: 0;
            scale: 0.5;
            overflow: visible;
            width: 100vw;
            height: 100vh;
            object-fit: cover;
            object-position: center;
            z-index: -1;
        }

        .download-container {
            position: fixed;
            bottom: 50px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1;
        }

        .download-btn {
            color: white;
            padding: 20px 40px;
            border-radius: var(--border-radius);
            font-size: 18px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 12px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            isolation: isolate;
            border: 1px solid transparent;
            cursor: pointer;
            background: var(--glass-bg);
            backdrop-filter: blur(var(--backdrop-blur));
            -webkit-backdrop-filter: blur(var(--backdrop-blur));
            box-shadow: var(--glass-shadow);
            overflow: hidden;
        }

        .download-btn::before {
            content: '';
            position: absolute;
            inset: 0;
            z-index: -1;
            border-radius: var(--border-radius);
            background: var(--glass-border);
            padding: 1px;
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: xor;
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .download-btn::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(255, 255, 255, 0.2),
                transparent);
            transition: left 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1;
            pointer-events: none;
        }

        .download-btn:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: var(--glass-hover-shadow);
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.15) 0%,
                rgba(255, 255, 255, 0.08) 50%,
                rgba(255, 255, 255, 0.15) 100%);
        }

        .download-btn:hover::after {
            left: 100%;
        }

        .download-btn:active {
            transform: translateY(-2px) scale(1.01);
            transition: all 0.1s ease;
        }

        .mac-logo {
            width: 20px;
            height: 20px;
            fill: currentColor;
            flex-shrink: 0;
        }
    </style>
</head>
<body>
    <video class="video-background" autoplay muted loop>
        <source src="speakmcp-web.mp4" type="video/mp4">
        Your browser does not support the video tag.
    </video>

    <div class="download-container">
        <a href="https://github.com/aj47/SpeakMCP/releases/download/v0.0.2/SpeakMCP-0.0.2-arm64.dmg" class="download-btn">
            <svg class="mac-logo" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
            </svg>
            Download for Mac
        </a>
    </div>


</body>
</html>
